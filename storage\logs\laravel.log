[2024-06-27 15:06:45] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:110)
[stacktrace]
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\PHP ALL\\school-management-system\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(88): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(130): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-06-27 15:06:45] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1349
Stack trace:
#0 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\PHP ALL\\scho...', 1349)
#1 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(576): include('E:\\PHP ALL\\scho...')
#2 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\PHP ALL\\scho...')
#3 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\helpers.php(110): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(130): collect(Array)
#5 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(106): Illuminate\Foundation\PackageManifest->build()
#6 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): Illuminate\Foundation\PackageManifest->getManifest()
#7 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#8 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#9 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(230): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#10 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(310): Illuminate\Foundation\Application->bootstrapWith(Array)
#11 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(127): Illuminate\Foundation\Console\Kernel->bootstrap()
#12 E:\PHP ALL\school-management-system\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#13 {main} {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1349
Stack trace:
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\PHP ALL\\\\scho...', 1349)
#1 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\PHP ALL\\\\scho...')
#2 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\PHP ALL\\\\scho...')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect(Array)
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 E:\\PHP ALL\\school-management-system\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main} at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-06-27 15:07:33] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:110)
[stacktrace]
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\PHP ALL\\school-management-system\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(88): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(130): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-06-27 15:07:33] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1349
Stack trace:
#0 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\PHP ALL\\scho...', 1349)
#1 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(576): include('E:\\PHP ALL\\scho...')
#2 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\PHP ALL\\scho...')
#3 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\helpers.php(110): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(130): collect(Array)
#5 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(106): Illuminate\Foundation\PackageManifest->build()
#6 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): Illuminate\Foundation\PackageManifest->getManifest()
#7 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#8 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#9 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(230): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#10 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(310): Illuminate\Foundation\Application->bootstrapWith(Array)
#11 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(127): Illuminate\Foundation\Console\Kernel->bootstrap()
#12 E:\PHP ALL\school-management-system\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#13 {main} {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1349
Stack trace:
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\PHP ALL\\\\scho...', 1349)
#1 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\PHP ALL\\\\scho...')
#2 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\PHP ALL\\\\scho...')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect(Array)
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 E:\\PHP ALL\\school-management-system\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main} at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-06-27 15:11:32] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:110)
[stacktrace]
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\PHP ALL\\school-management-system\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(88): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(130): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-06-27 15:11:32] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1349
Stack trace:
#0 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\PHP ALL\\scho...', 1349)
#1 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(576): include('E:\\PHP ALL\\scho...')
#2 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\PHP ALL\\scho...')
#3 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\helpers.php(110): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(130): collect(Array)
#5 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(106): Illuminate\Foundation\PackageManifest->build()
#6 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): Illuminate\Foundation\PackageManifest->getManifest()
#7 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#8 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#9 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(230): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#10 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(310): Illuminate\Foundation\Application->bootstrapWith(Array)
#11 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(127): Illuminate\Foundation\Console\Kernel->bootstrap()
#12 E:\PHP ALL\school-management-system\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#13 {main} {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1349
Stack trace:
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\PHP ALL\\\\scho...', 1349)
#1 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\PHP ALL\\\\scho...')
#2 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\PHP ALL\\\\scho...')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect(Array)
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 E:\\PHP ALL\\school-management-system\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main} at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-06-27 15:12:05] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:110)
[stacktrace]
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\PHP ALL\\school-management-system\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(88): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(130): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-06-27 15:12:05] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1349
Stack trace:
#0 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\PHP ALL\\scho...', 1349)
#1 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(576): include('E:\\PHP ALL\\scho...')
#2 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\PHP ALL\\scho...')
#3 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\helpers.php(110): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(130): collect(Array)
#5 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(106): Illuminate\Foundation\PackageManifest->build()
#6 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): Illuminate\Foundation\PackageManifest->getManifest()
#7 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#8 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#9 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(230): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#10 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(310): Illuminate\Foundation\Application->bootstrapWith(Array)
#11 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(127): Illuminate\Foundation\Console\Kernel->bootstrap()
#12 E:\PHP ALL\school-management-system\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#13 {main} {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1349
Stack trace:
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\PHP ALL\\\\scho...', 1349)
#1 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\PHP ALL\\\\scho...')
#2 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\PHP ALL\\\\scho...')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect(Array)
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 E:\\PHP ALL\\school-management-system\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main} at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-06-27 15:12:17] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:110)
[stacktrace]
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\PHP ALL\\school-management-system\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(88): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(130): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-06-27 15:12:17] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1349
Stack trace:
#0 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\PHP ALL\\scho...', 1349)
#1 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(576): include('E:\\PHP ALL\\scho...')
#2 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\PHP ALL\\scho...')
#3 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\helpers.php(110): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(130): collect(Array)
#5 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(106): Illuminate\Foundation\PackageManifest->build()
#6 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): Illuminate\Foundation\PackageManifest->getManifest()
#7 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#8 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#9 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(230): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#10 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(310): Illuminate\Foundation\Application->bootstrapWith(Array)
#11 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(127): Illuminate\Foundation\Console\Kernel->bootstrap()
#12 E:\PHP ALL\school-management-system\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#13 {main} {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1349
Stack trace:
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\PHP ALL\\\\scho...', 1349)
#1 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\PHP ALL\\\\scho...')
#2 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\PHP ALL\\\\scho...')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect(Array)
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 E:\\PHP ALL\\school-management-system\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main} at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-06-27 15:12:17] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:110)
[stacktrace]
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\PHP ALL\\school-management-system\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(88): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(130): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-06-27 15:12:17] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1349
Stack trace:
#0 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\PHP ALL\\scho...', 1349)
#1 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(576): include('E:\\PHP ALL\\scho...')
#2 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\PHP ALL\\scho...')
#3 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\helpers.php(110): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(130): collect(Array)
#5 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(106): Illuminate\Foundation\PackageManifest->build()
#6 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): Illuminate\Foundation\PackageManifest->getManifest()
#7 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#8 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#9 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(230): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#10 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(310): Illuminate\Foundation\Application->bootstrapWith(Array)
#11 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(127): Illuminate\Foundation\Console\Kernel->bootstrap()
#12 E:\PHP ALL\school-management-system\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#13 {main} {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1349
Stack trace:
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\PHP ALL\\\\scho...', 1349)
#1 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\PHP ALL\\\\scho...')
#2 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\PHP ALL\\\\scho...')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect(Array)
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 E:\\PHP ALL\\school-management-system\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main} at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-06-27 15:12:18] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:110)
[stacktrace]
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\PHP ALL\\school-management-system\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(88): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(130): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-06-27 15:12:18] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1349
Stack trace:
#0 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\PHP ALL\\scho...', 1349)
#1 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(576): include('E:\\PHP ALL\\scho...')
#2 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\PHP ALL\\scho...')
#3 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\helpers.php(110): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(130): collect(Array)
#5 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(106): Illuminate\Foundation\PackageManifest->build()
#6 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): Illuminate\Foundation\PackageManifest->getManifest()
#7 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#8 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#9 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(230): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#10 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(310): Illuminate\Foundation\Application->bootstrapWith(Array)
#11 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(127): Illuminate\Foundation\Console\Kernel->bootstrap()
#12 E:\PHP ALL\school-management-system\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#13 {main} {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1349
Stack trace:
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\PHP ALL\\\\scho...', 1349)
#1 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\PHP ALL\\\\scho...')
#2 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\PHP ALL\\\\scho...')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect(Array)
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 E:\\PHP ALL\\school-management-system\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main} at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-06-27 15:12:25] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:110)
[stacktrace]
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\PHP ALL\\school-management-system\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(88): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(130): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-06-27 15:12:25] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1349
Stack trace:
#0 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\PHP ALL\\scho...', 1349)
#1 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(576): include('E:\\PHP ALL\\scho...')
#2 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\PHP ALL\\scho...')
#3 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\helpers.php(110): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(130): collect(Array)
#5 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(106): Illuminate\Foundation\PackageManifest->build()
#6 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): Illuminate\Foundation\PackageManifest->getManifest()
#7 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#8 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#9 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(230): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#10 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(310): Illuminate\Foundation\Application->bootstrapWith(Array)
#11 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(127): Illuminate\Foundation\Console\Kernel->bootstrap()
#12 E:\PHP ALL\school-management-system\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#13 {main} {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1349
Stack trace:
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\PHP ALL\\\\scho...', 1349)
#1 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\PHP ALL\\\\scho...')
#2 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\PHP ALL\\\\scho...')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect(Array)
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 E:\\PHP ALL\\school-management-system\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main} at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-06-27 15:13:52] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:110)
[stacktrace]
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\PHP ALL\\school-management-system\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(88): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(130): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-06-27 15:13:52] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1349
Stack trace:
#0 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\PHP ALL\\scho...', 1349)
#1 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(576): include('E:\\PHP ALL\\scho...')
#2 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\PHP ALL\\scho...')
#3 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\helpers.php(110): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(130): collect(Array)
#5 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(106): Illuminate\Foundation\PackageManifest->build()
#6 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): Illuminate\Foundation\PackageManifest->getManifest()
#7 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#8 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#9 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(230): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#10 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(310): Illuminate\Foundation\Application->bootstrapWith(Array)
#11 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(127): Illuminate\Foundation\Console\Kernel->bootstrap()
#12 E:\PHP ALL\school-management-system\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#13 {main} {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1349
Stack trace:
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\PHP ALL\\\\scho...', 1349)
#1 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\PHP ALL\\\\scho...')
#2 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\PHP ALL\\\\scho...')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect(Array)
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 E:\\PHP ALL\\school-management-system\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main} at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-06-27 15:17:56] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:110)
[stacktrace]
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\PHP ALL\\school-management-system\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(88): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(130): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-06-27 15:17:56] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1349
Stack trace:
#0 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\PHP ALL\\scho...', 1349)
#1 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(576): include('E:\\PHP ALL\\scho...')
#2 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\PHP ALL\\scho...')
#3 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\helpers.php(110): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(130): collect(Array)
#5 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(106): Illuminate\Foundation\PackageManifest->build()
#6 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): Illuminate\Foundation\PackageManifest->getManifest()
#7 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#8 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#9 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(230): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#10 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(310): Illuminate\Foundation\Application->bootstrapWith(Array)
#11 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(127): Illuminate\Foundation\Console\Kernel->bootstrap()
#12 E:\PHP ALL\school-management-system\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#13 {main} {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1349
Stack trace:
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\PHP ALL\\\\scho...', 1349)
#1 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\PHP ALL\\\\scho...')
#2 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\PHP ALL\\\\scho...')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect(Array)
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 E:\\PHP ALL\\school-management-system\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main} at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-06-27 15:18:21] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:110)
[stacktrace]
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\PHP ALL\\school-management-system\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(88): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(130): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-06-27 15:18:21] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1349
Stack trace:
#0 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\PHP ALL\\scho...', 1349)
#1 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(576): include('E:\\PHP ALL\\scho...')
#2 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\PHP ALL\\scho...')
#3 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\helpers.php(110): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(130): collect(Array)
#5 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(106): Illuminate\Foundation\PackageManifest->build()
#6 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): Illuminate\Foundation\PackageManifest->getManifest()
#7 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#8 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#9 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(230): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#10 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(310): Illuminate\Foundation\Application->bootstrapWith(Array)
#11 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(127): Illuminate\Foundation\Console\Kernel->bootstrap()
#12 E:\PHP ALL\school-management-system\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#13 {main} {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1349
Stack trace:
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\PHP ALL\\\\scho...', 1349)
#1 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\PHP ALL\\\\scho...')
#2 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\PHP ALL\\\\scho...')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect(Array)
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 E:\\PHP ALL\\school-management-system\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main} at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-06-27 15:19:03] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:110)
[stacktrace]
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\PHP ALL\\school-management-system\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(88): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(130): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-06-27 15:19:03] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1349
Stack trace:
#0 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\PHP ALL\\scho...', 1349)
#1 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(576): include('E:\\PHP ALL\\scho...')
#2 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\PHP ALL\\scho...')
#3 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\helpers.php(110): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(130): collect(Array)
#5 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(106): Illuminate\Foundation\PackageManifest->build()
#6 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): Illuminate\Foundation\PackageManifest->getManifest()
#7 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#8 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#9 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(230): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#10 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(310): Illuminate\Foundation\Application->bootstrapWith(Array)
#11 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(127): Illuminate\Foundation\Console\Kernel->bootstrap()
#12 E:\PHP ALL\school-management-system\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#13 {main} {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1349
Stack trace:
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\PHP ALL\\\\scho...', 1349)
#1 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\PHP ALL\\\\scho...')
#2 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\PHP ALL\\\\scho...')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect(Array)
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 E:\\PHP ALL\\school-management-system\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main} at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-06-27 15:20:38] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:110)
[stacktrace]
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\PHP ALL\\school-management-system\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(88): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(130): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-06-27 15:20:38] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1349
Stack trace:
#0 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\PHP ALL\\scho...', 1349)
#1 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(576): include('E:\\PHP ALL\\scho...')
#2 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\PHP ALL\\scho...')
#3 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\helpers.php(110): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(130): collect(Array)
#5 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(106): Illuminate\Foundation\PackageManifest->build()
#6 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): Illuminate\Foundation\PackageManifest->getManifest()
#7 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#8 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#9 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(230): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#10 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(310): Illuminate\Foundation\Application->bootstrapWith(Array)
#11 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(127): Illuminate\Foundation\Console\Kernel->bootstrap()
#12 E:\PHP ALL\school-management-system\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#13 {main} {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1349
Stack trace:
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\PHP ALL\\\\scho...', 1349)
#1 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\PHP ALL\\\\scho...')
#2 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\PHP ALL\\\\scho...')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect(Array)
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 E:\\PHP ALL\\school-management-system\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main} at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-06-27 15:21:26] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:110)
[stacktrace]
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\PHP ALL\\school-management-system\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(88): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(130): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-06-27 15:21:26] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1349
Stack trace:
#0 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\PHP ALL\\scho...', 1349)
#1 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(576): include('E:\\PHP ALL\\scho...')
#2 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\PHP ALL\\scho...')
#3 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\helpers.php(110): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(130): collect(Array)
#5 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(106): Illuminate\Foundation\PackageManifest->build()
#6 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): Illuminate\Foundation\PackageManifest->getManifest()
#7 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#8 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#9 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(230): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#10 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(310): Illuminate\Foundation\Application->bootstrapWith(Array)
#11 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(127): Illuminate\Foundation\Console\Kernel->bootstrap()
#12 E:\PHP ALL\school-management-system\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#13 {main} {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1349
Stack trace:
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\PHP ALL\\\\scho...', 1349)
#1 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\PHP ALL\\\\scho...')
#2 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\PHP ALL\\\\scho...')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect(Array)
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 E:\\PHP ALL\\school-management-system\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main} at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-06-27 15:23:03] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:110)
[stacktrace]
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\PHP ALL\\school-management-system\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(88): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(130): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-06-27 15:23:03] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1349
Stack trace:
#0 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\PHP ALL\\scho...', 1349)
#1 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(576): include('E:\\PHP ALL\\scho...')
#2 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\PHP ALL\\scho...')
#3 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\helpers.php(110): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(130): collect(Array)
#5 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(106): Illuminate\Foundation\PackageManifest->build()
#6 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): Illuminate\Foundation\PackageManifest->getManifest()
#7 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#8 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#9 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(230): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#10 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(310): Illuminate\Foundation\Application->bootstrapWith(Array)
#11 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(127): Illuminate\Foundation\Console\Kernel->bootstrap()
#12 E:\PHP ALL\school-management-system\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#13 {main} {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1349
Stack trace:
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\PHP ALL\\\\scho...', 1349)
#1 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\PHP ALL\\\\scho...')
#2 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\PHP ALL\\\\scho...')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect(Array)
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 E:\\PHP ALL\\school-management-system\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main} at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-06-27 15:23:03] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:110)
[stacktrace]
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\PHP ALL\\school-management-system\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(88): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(130): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-06-27 15:23:03] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1349
Stack trace:
#0 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\PHP ALL\\scho...', 1349)
#1 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(576): include('E:\\PHP ALL\\scho...')
#2 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\PHP ALL\\scho...')
#3 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\helpers.php(110): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(130): collect(Array)
#5 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(106): Illuminate\Foundation\PackageManifest->build()
#6 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): Illuminate\Foundation\PackageManifest->getManifest()
#7 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#8 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#9 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(230): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#10 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(310): Illuminate\Foundation\Application->bootstrapWith(Array)
#11 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(127): Illuminate\Foundation\Console\Kernel->bootstrap()
#12 E:\PHP ALL\school-management-system\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#13 {main} {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1349
Stack trace:
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\PHP ALL\\\\scho...', 1349)
#1 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\PHP ALL\\\\scho...')
#2 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\PHP ALL\\\\scho...')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect(Array)
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 E:\\PHP ALL\\school-management-system\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main} at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-06-27 15:23:03] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:110)
[stacktrace]
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\PHP ALL\\school-management-system\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(88): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(130): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-06-27 15:23:03] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1349
Stack trace:
#0 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\PHP ALL\\scho...', 1349)
#1 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(576): include('E:\\PHP ALL\\scho...')
#2 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\PHP ALL\\scho...')
#3 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\helpers.php(110): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(130): collect(Array)
#5 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(106): Illuminate\Foundation\PackageManifest->build()
#6 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): Illuminate\Foundation\PackageManifest->getManifest()
#7 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#8 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#9 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(230): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#10 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(310): Illuminate\Foundation\Application->bootstrapWith(Array)
#11 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(127): Illuminate\Foundation\Console\Kernel->bootstrap()
#12 E:\PHP ALL\school-management-system\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#13 {main} {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1349
Stack trace:
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\PHP ALL\\\\scho...', 1349)
#1 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\PHP ALL\\\\scho...')
#2 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\PHP ALL\\\\scho...')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect(Array)
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 E:\\PHP ALL\\school-management-system\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main} at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-06-27 15:27:59] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:110)
[stacktrace]
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\PHP ALL\\school-management-system\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(88): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(130): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-06-27 15:27:59] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1349
Stack trace:
#0 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\PHP ALL\\scho...', 1349)
#1 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(576): include('E:\\PHP ALL\\scho...')
#2 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\PHP ALL\\scho...')
#3 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\helpers.php(110): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(130): collect(Array)
#5 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(106): Illuminate\Foundation\PackageManifest->build()
#6 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): Illuminate\Foundation\PackageManifest->getManifest()
#7 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#8 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#9 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(230): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#10 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(310): Illuminate\Foundation\Application->bootstrapWith(Array)
#11 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(127): Illuminate\Foundation\Console\Kernel->bootstrap()
#12 E:\PHP ALL\school-management-system\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#13 {main} {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1349
Stack trace:
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\PHP ALL\\\\scho...', 1349)
#1 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\PHP ALL\\\\scho...')
#2 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\PHP ALL\\\\scho...')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect(Array)
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 E:\\PHP ALL\\school-management-system\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main} at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-06-27 15:34:51] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:110)
[stacktrace]
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\PHP ALL\\school-management-system\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(88): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(130): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-06-27 15:34:51] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1349
Stack trace:
#0 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\PHP ALL\\scho...', 1349)
#1 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(576): include('E:\\PHP ALL\\scho...')
#2 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\PHP ALL\\scho...')
#3 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\helpers.php(110): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(130): collect(Array)
#5 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(106): Illuminate\Foundation\PackageManifest->build()
#6 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): Illuminate\Foundation\PackageManifest->getManifest()
#7 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#8 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#9 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(230): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#10 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(310): Illuminate\Foundation\Application->bootstrapWith(Array)
#11 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(127): Illuminate\Foundation\Console\Kernel->bootstrap()
#12 E:\PHP ALL\school-management-system\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#13 {main} {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1349
Stack trace:
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\PHP ALL\\\\scho...', 1349)
#1 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\PHP ALL\\\\scho...')
#2 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\PHP ALL\\\\scho...')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect(Array)
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 E:\\PHP ALL\\school-management-system\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main} at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-06-27 15:37:03] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:110)
[stacktrace]
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\PHP ALL\\school-management-system\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(88): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(130): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-06-27 15:37:03] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1349
Stack trace:
#0 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\PHP ALL\\scho...', 1349)
#1 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(576): include('E:\\PHP ALL\\scho...')
#2 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\PHP ALL\\scho...')
#3 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\helpers.php(110): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(130): collect(Array)
#5 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(106): Illuminate\Foundation\PackageManifest->build()
#6 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): Illuminate\Foundation\PackageManifest->getManifest()
#7 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#8 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#9 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(230): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#10 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(310): Illuminate\Foundation\Application->bootstrapWith(Array)
#11 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(127): Illuminate\Foundation\Console\Kernel->bootstrap()
#12 E:\PHP ALL\school-management-system\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#13 {main} {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1349
Stack trace:
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\PHP ALL\\\\scho...', 1349)
#1 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\PHP ALL\\\\scho...')
#2 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\PHP ALL\\\\scho...')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect(Array)
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 E:\\PHP ALL\\school-management-system\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main} at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-06-27 15:48:14] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:110)
[stacktrace]
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\PHP ALL\\school-management-system\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(88): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(130): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-06-27 15:48:14] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1349
Stack trace:
#0 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\PHP ALL\\scho...', 1349)
#1 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(576): include('E:\\PHP ALL\\scho...')
#2 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\PHP ALL\\scho...')
#3 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\helpers.php(110): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(130): collect(Array)
#5 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(106): Illuminate\Foundation\PackageManifest->build()
#6 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): Illuminate\Foundation\PackageManifest->getManifest()
#7 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#8 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#9 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(230): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#10 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(310): Illuminate\Foundation\Application->bootstrapWith(Array)
#11 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(127): Illuminate\Foundation\Console\Kernel->bootstrap()
#12 E:\PHP ALL\school-management-system\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#13 {main} {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1349
Stack trace:
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\PHP ALL\\\\scho...', 1349)
#1 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\PHP ALL\\\\scho...')
#2 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\PHP ALL\\\\scho...')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect(Array)
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 E:\\PHP ALL\\school-management-system\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main} at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-06-27 15:48:23] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:110)
[stacktrace]
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\PHP ALL\\school-management-system\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(88): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(130): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-06-27 15:48:23] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1349
Stack trace:
#0 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\PHP ALL\\scho...', 1349)
#1 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(576): include('E:\\PHP ALL\\scho...')
#2 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\PHP ALL\\scho...')
#3 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\helpers.php(110): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(130): collect(Array)
#5 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(106): Illuminate\Foundation\PackageManifest->build()
#6 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): Illuminate\Foundation\PackageManifest->getManifest()
#7 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#8 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#9 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(230): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#10 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(310): Illuminate\Foundation\Application->bootstrapWith(Array)
#11 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(127): Illuminate\Foundation\Console\Kernel->bootstrap()
#12 E:\PHP ALL\school-management-system\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#13 {main} {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1349
Stack trace:
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\PHP ALL\\\\scho...', 1349)
#1 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\PHP ALL\\\\scho...')
#2 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\PHP ALL\\\\scho...')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect(Array)
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 E:\\PHP ALL\\school-management-system\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main} at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2024-06-27 15:54:26] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (Error(code: 0): Class \"Illuminate\\Support\\Collection\" not found at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php:110)
[stacktrace]
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect(Array)
#1 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver(Array)
#2 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve('stack')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get('stack')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error('During inherita...', Array)
#6 E:\\PHP ALL\\school-management-system\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(88): App\\Exceptions\\Handler->report(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(130): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Symfony\\Component\\ErrorHandler\\Error\\FatalError))
#9 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#10 {main}
"} 
[2024-06-27 15:54:26] laravel.ERROR: During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\Support\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php:1349
Stack trace:
#0 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\Collection.php(11): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\PHP ALL\\scho...', 1349)
#1 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(576): include('E:\\PHP ALL\\scho...')
#2 E:\PHP ALL\school-management-system\vendor\composer\ClassLoader.php(427): Composer\Autoload\{closure}('E:\\PHP ALL\\scho...')
#3 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Support\helpers.php(110): Composer\Autoload\ClassLoader->loadClass('Illuminate\\Supp...')
#4 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(130): collect(Array)
#5 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(106): Illuminate\Foundation\PackageManifest->build()
#6 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(89): Illuminate\Foundation\PackageManifest->getManifest()
#7 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\PackageManifest.php(78): Illuminate\Foundation\PackageManifest->config('aliases')
#8 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\RegisterFacades.php(26): Illuminate\Foundation\PackageManifest->aliases()
#9 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(230): Illuminate\Foundation\Bootstrap\RegisterFacades->bootstrap(Object(Illuminate\Foundation\Application))
#10 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(310): Illuminate\Foundation\Application->bootstrapWith(Array)
#11 E:\PHP ALL\school-management-system\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(127): Illuminate\Foundation\Console\Kernel->bootstrap()
#12 E:\PHP ALL\school-management-system\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#13 {main} {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): During inheritance of ArrayAccess: Uncaught ErrorException: Return type of Illuminate\\Support\\Collection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:1349
Stack trace:
#0 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php(11): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8192, 'Return type of ...', 'E:\\\\PHP ALL\\\\scho...', 1349)
#1 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(576): include('E:\\\\PHP ALL\\\\scho...')
#2 E:\\PHP ALL\\school-management-system\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('E:\\\\PHP ALL\\\\scho...')
#3 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Supp...')
#4 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect(Array)
#5 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 E:\\PHP ALL\\school-management-system\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main} at E:\\PHP ALL\\school-management-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:11)
[stacktrace]
#0 {main}
"} 
[2025-07-17 08:33:50] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::times(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:40)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error()
#10 C:\\xampp\\htdocs\\school\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(366): App\\Exceptions\\Handler->report()
#12 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(131): Illuminate\\Foundation\\Console\\Kernel->reportException()
#13 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#14 {main}
"} 
[2025-07-17 08:33:51] laravel.ERROR: Illuminate\Support\Collection::times(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::times(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:40)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#10 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#13 {main}
"} 
[2025-07-17 08:33:57] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::times(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:40)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error()
#10 C:\\xampp\\htdocs\\school\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(366): App\\Exceptions\\Handler->report()
#12 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(131): Illuminate\\Foundation\\Console\\Kernel->reportException()
#13 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#14 {main}
"} 
[2025-07-17 08:33:57] laravel.ERROR: Illuminate\Support\Collection::times(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::times(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:40)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#10 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#13 {main}
"} 
[2025-07-17 08:34:01] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::times(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:40)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error()
#10 C:\\xampp\\htdocs\\school\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(366): App\\Exceptions\\Handler->report()
#12 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(131): Illuminate\\Foundation\\Console\\Kernel->reportException()
#13 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#14 {main}
"} 
[2025-07-17 08:34:01] laravel.ERROR: Illuminate\Support\Collection::times(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::times(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:40)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#10 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#13 {main}
"} 
[2025-07-17 08:40:45] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::times(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:40)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error()
#10 C:\\xampp\\htdocs\\school\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(366): App\\Exceptions\\Handler->report()
#12 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(131): Illuminate\\Foundation\\Console\\Kernel->reportException()
#13 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#14 {main}
"} 
[2025-07-17 08:40:45] laravel.ERROR: Illuminate\Support\Collection::times(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::times(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:40)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#10 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#13 {main}
"} 
[2025-07-17 08:42:18] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::times(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:40)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error()
#10 C:\\xampp\\htdocs\\school\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(366): App\\Exceptions\\Handler->report()
#12 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(131): Illuminate\\Foundation\\Console\\Kernel->reportException()
#13 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#14 {main}
"} 
[2025-07-17 08:42:18] laravel.ERROR: Illuminate\Support\Collection::times(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::times(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:40)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#10 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#13 {main}
"} 
[2025-07-17 08:44:56] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:349)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error()
#10 C:\\xampp\\htdocs\\school\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(366): App\\Exceptions\\Handler->report()
#12 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(131): Illuminate\\Foundation\\Console\\Kernel->reportException()
#13 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#14 {main}
"} 
[2025-07-17 08:44:56] laravel.ERROR: Illuminate\Support\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:349)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#10 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#13 {main}
"} 
[2025-07-17 08:44:57] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:349)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error()
#10 C:\\xampp\\htdocs\\school\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(366): App\\Exceptions\\Handler->report()
#12 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(131): Illuminate\\Foundation\\Console\\Kernel->reportException()
#13 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#14 {main}
"} 
[2025-07-17 08:44:57] laravel.ERROR: Illuminate\Support\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:349)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#10 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#13 {main}
"} 
[2025-07-17 08:44:58] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:349)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error()
#10 C:\\xampp\\htdocs\\school\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(366): App\\Exceptions\\Handler->report()
#12 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(131): Illuminate\\Foundation\\Console\\Kernel->reportException()
#13 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#14 {main}
"} 
[2025-07-17 08:44:58] laravel.ERROR: Illuminate\Support\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:349)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#10 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#13 {main}
"} 
[2025-07-17 09:10:22] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:349)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error()
#10 C:\\xampp\\htdocs\\school\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(366): App\\Exceptions\\Handler->report()
#12 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(131): Illuminate\\Foundation\\Console\\Kernel->reportException()
#13 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#14 {main}
"} 
[2025-07-17 09:10:22] laravel.ERROR: Illuminate\Support\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:349)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#10 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#13 {main}
"} 
[2025-07-17 09:12:07] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:349)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error()
#10 C:\\xampp\\htdocs\\school\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(366): App\\Exceptions\\Handler->report()
#12 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(131): Illuminate\\Foundation\\Console\\Kernel->reportException()
#13 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#14 {main}
"} 
[2025-07-17 09:12:07] laravel.ERROR: Illuminate\Support\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:349)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#10 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#13 {main}
"} 
[2025-07-17 09:14:04] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:349)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error()
#10 C:\\xampp\\htdocs\\school\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(366): App\\Exceptions\\Handler->report()
#12 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(131): Illuminate\\Foundation\\Console\\Kernel->reportException()
#13 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#14 {main}
"} 
[2025-07-17 09:14:04] laravel.ERROR: Illuminate\Support\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:349)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#10 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#13 {main}
"} 
[2025-07-17 09:15:56] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:349)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error()
#10 C:\\xampp\\htdocs\\school\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(366): App\\Exceptions\\Handler->report()
#12 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(131): Illuminate\\Foundation\\Console\\Kernel->reportException()
#13 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#14 {main}
"} 
[2025-07-17 09:15:56] laravel.ERROR: Illuminate\Support\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:349)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#10 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#13 {main}
"} 
[2025-07-17 09:21:11] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:349)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error()
#10 C:\\xampp\\htdocs\\school\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(366): App\\Exceptions\\Handler->report()
#12 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(131): Illuminate\\Foundation\\Console\\Kernel->reportException()
#13 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#14 {main}
"} 
[2025-07-17 09:21:11] laravel.ERROR: Illuminate\Support\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:349)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#10 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#13 {main}
"} 
[2025-07-17 09:28:06] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:349)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error()
#10 C:\\xampp\\htdocs\\school\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(366): App\\Exceptions\\Handler->report()
#12 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(131): Illuminate\\Foundation\\Console\\Kernel->reportException()
#13 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#14 {main}
"} 
[2025-07-17 09:28:06] laravel.ERROR: Illuminate\Support\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:349)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#10 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#13 {main}
"} 
[2025-07-17 09:32:32] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:349)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(240): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(202): Illuminate\\Log\\LogManager->createStackDriver()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(118): Illuminate\\Log\\LogManager->resolve()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(98): Illuminate\\Log\\LogManager->get()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(543): Illuminate\\Log\\LogManager->driver()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(118): Illuminate\\Log\\LogManager->error()
#10 C:\\xampp\\htdocs\\school\\app\\Exceptions\\Handler.php(39): Illuminate\\Foundation\\Exceptions\\Handler->report()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(366): App\\Exceptions\\Handler->report()
#12 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(131): Illuminate\\Foundation\\Console\\Kernel->reportException()
#13 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#14 {main}
"} 
[2025-07-17 09:32:32] laravel.ERROR: Illuminate\Support\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead {"exception":"[object] (ErrorException(code: 0): Illuminate\\Support\\Collection::filter(): Implicitly marking parameter $callback as nullable is deprecated, the explicit nullable type must be used instead at C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Collection.php:349)
[stacktrace]
#0 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(576): include()
#2 C:\\xampp\\htdocs\\school\\vendor\\composer\\ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}()
#3 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(110): Composer\\Autoload\\ClassLoader->loadClass()
#4 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(130): collect()
#5 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(106): Illuminate\\Foundation\\PackageManifest->build()
#6 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(89): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(78): Illuminate\\Foundation\\PackageManifest->config()
#8 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap()
#10 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith()
#11 C:\\xampp\\htdocs\\school\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 C:\\xampp\\htdocs\\school\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#13 {main}
"} 
